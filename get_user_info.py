#!/usr/bin/env python3
"""Script to get user info and secUid for a TikTok username."""

import http.client
import json
import sys

def get_user_info(username):
    """Get user info including secUid for a TikTok username."""
    print(f"Getting user info for @{username}...")
    
    try:
        conn = http.client.HTTPSConnection("tiktok-api23.p.rapidapi.com")

        headers = {
            'x-rapidapi-key': "**************************************************",
            'x-rapidapi-host': "tiktok-api23.p.rapidapi.com"
        }

        endpoint = f"/api/user/info?uniqueId={username}"
        
        print(f"Making request to: {endpoint}")
        conn.request("GET", endpoint, headers=headers)

        res = conn.getresponse()
        data = res.read()

        print(f"Status: {res.status}")
        
        if res.status == 200:
            try:
                response_data = json.loads(data.decode("utf-8"))
                print("Successfully parsed JSON response")
                
                # Extract key information
                if 'userInfo' in response_data and 'user' in response_data['userInfo']:
                    user = response_data['userInfo']['user']
                    stats = response_data['userInfo'].get('stats', {})
                    
                    print("\n" + "="*50)
                    print("USER INFORMATION")
                    print("="*50)
                    print(f"Username: @{user.get('uniqueId', 'N/A')}")
                    print(f"Display Name: {user.get('nickname', 'N/A')}")
                    print(f"Verified: {user.get('verified', False)}")
                    print(f"Private Account: {user.get('privateAccount', False)}")
                    print(f"Bio: {user.get('signature', 'N/A')}")
                    
                    print(f"\nSecUid: {user.get('secUid', 'N/A')}")
                    print(f"User ID: {user.get('id', 'N/A')}")
                    
                    print(f"\nFollowers: {stats.get('followerCount', 0):,}")
                    print(f"Following: {stats.get('followingCount', 0):,}")
                    print(f"Videos: {stats.get('videoCount', 0):,}")
                    print(f"Likes: {stats.get('heartCount', 0):,}")
                    
                    # Return the secUid for use in other scripts
                    return user.get('secUid')
                else:
                    print("Could not find user information in response")
                    print("Response structure:")
                    print(json.dumps(response_data, indent=2)[:1000])
                    return None
                    
            except json.JSONDecodeError as e:
                print(f"Failed to parse JSON: {e}")
                print("Raw response:")
                print(data.decode("utf-8")[:1000])
                return None
        else:
            print(f"API request failed with status {res.status}")
            print("Response:")
            print(data.decode("utf-8"))
            return None
            
    except Exception as e:
        print(f"Error: {e}")
        return None
    finally:
        try:
            conn.close()
        except:
            pass

def main():
    """Main function."""
    if len(sys.argv) > 1:
        username = sys.argv[1]
    else:
        username = "naomifoxes"
    
    secUid = get_user_info(username)
    
    if secUid:
        print(f"\n" + "="*50)
        print("CONFIGURATION UPDATE")
        print("="*50)
        print(f"Update your .env file with:")
        print(f"TIKTOK_SEC_UID={secUid}")
        print(f"TIKTOK_USERNAME={username}")
    else:
        print("\nFailed to get secUid")

if __name__ == "__main__":
    main()
