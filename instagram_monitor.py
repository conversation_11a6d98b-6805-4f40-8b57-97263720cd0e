"""Instagram monitoring using RapidAPI."""

import logging
import json
import http.client
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any, Tuple

from config import Config

logger = logging.getLogger(__name__)

class InstagramMonitor:
    """Instagram monitor using RapidAPI for post detection."""
    
    def __init__(self):
        self.config = Config()
    
    def _make_api_request(self, username: str) -> Optional[Dict[str, Any]]:
        """Make request to Instagram API via RapidAPI for a specific account."""
        try:
            conn = http.client.HTTPSConnection("instagram-scraper-api2.p.rapidapi.com")
            
            headers = {
                'x-rapidapi-key': self.config.RAPIDAPI_KEY,
                'x-rapidapi-host': "instagram-scraper-api2.p.rapidapi.com"
            }
            
            # Request recent posts for the user
            endpoint = f"/v1/posts?username_or_id_or_url={username}"
            
            logger.info(f"Making Instagram API request for @{username}")
            conn.request("GET", endpoint, headers=headers)
            
            res = conn.getresponse()
            data = res.read()
            
            if res.status == 200:
                response_data = json.loads(data.decode("utf-8"))
                logger.info(f"Successfully retrieved Instagram data for @{username}")
                return response_data
            else:
                logger.error(f"Instagram API request failed for @{username} with status {res.status}: {data.decode('utf-8')}")
                return None
                
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse Instagram API response as JSON for @{username}: {e}")
            return None
        except Exception as e:
            logger.error(f"Instagram API request failed for @{username}: {e}")
            return None
        finally:
            try:
                conn.close()
            except:
                pass
    
    def _parse_post_dates(self, api_response: Dict[str, Any], username: str) -> List[datetime]:
        """Parse post dates from Instagram API response."""
        post_dates = []
        
        try:
            # Get posts from the response
            posts = []
            if 'data' in api_response and 'items' in api_response['data']:
                posts = api_response['data']['items']
            elif 'items' in api_response:
                posts = api_response['items']
            
            logger.info(f"Found {len(posts)} Instagram posts for @{username}")
            
            for i, post in enumerate(posts):
                try:
                    # Get created_at timestamp (Instagram uses created_at or created_at_utc)
                    timestamp = None
                    timestamp_fields = ['created_at', 'created_at_utc', 'timestamp', 'taken_at']
                    
                    for field in timestamp_fields:
                        if field in post:
                            timestamp = post[field]
                            break
                    
                    if timestamp:
                        # Handle both seconds and milliseconds timestamps
                        if isinstance(timestamp, str):
                            timestamp = int(timestamp)
                        
                        if timestamp > 1000000000000:  # Milliseconds
                            timestamp = timestamp / 1000
                        
                        post_date = datetime.fromtimestamp(timestamp)
                        post_dates.append(post_date)
                        logger.debug(f"@{username} Instagram Post {i+1}: {post_date.strftime('%Y-%m-%d %H:%M:%S')}")
                    else:
                        logger.debug(f"@{username} Instagram Post {i+1}: No timestamp found in fields {list(post.keys())}")
                    
                except (ValueError, KeyError, TypeError) as e:
                    logger.debug(f"@{username} Instagram Post {i+1}: Could not parse timestamp: {e}")
                    continue
            
            logger.info(f"Successfully parsed {len(post_dates)} Instagram post dates for @{username}")
            return post_dates
            
        except Exception as e:
            logger.error(f"Error parsing Instagram post dates for @{username}: {e}")
            return []
    
    def check_account_posts_today(self, username: str) -> Tuple[bool, List[datetime]]:
        """Check if there were any Instagram posts made today for a specific account."""
        try:
            logger.info(f"Checking Instagram posts for @{username}...")
            
            # Make API request
            api_response = self._make_api_request(username)
            if not api_response:
                logger.error(f"Failed to get data from Instagram API for @{username}")
                return False, []
            
            # Parse post dates
            post_dates = self._parse_post_dates(api_response, username)
            
            if not post_dates:
                logger.info(f"No Instagram posts found for @{username}")
                return False, []
            
            # Check if any posts were made today
            today = datetime.now().date()
            today_posts = []
            
            for post_date in post_dates:
                if post_date.date() == today:
                    today_posts.append(post_date)
            
            if today_posts:
                logger.info(f"@{username} Instagram: Found {len(today_posts)} post(s) from today:")
                for post_date in today_posts:
                    logger.info(f"  - Post at: {post_date.strftime('%Y-%m-%d %H:%M:%S')}")
                return True, today_posts
            else:
                logger.info(f"@{username} Instagram: No posts found from today")
                # Show the most recent posts for debugging
                if post_dates:
                    recent_posts = sorted(post_dates, reverse=True)[:2]
                    logger.info(f"@{username} Instagram: Most recent posts:")
                    for post_date in recent_posts:
                        logger.info(f"  - {post_date.strftime('%Y-%m-%d %H:%M:%S')}")
                return False, []
            
        except Exception as e:
            logger.error(f"Error checking Instagram posts for @{username}: {e}")
            return False, []
    
    def check_all_accounts_today(self, instagram_accounts: List[str]) -> Dict[str, Any]:
        """Check all Instagram accounts for posts today."""
        results = {
            'accounts_checked': [],
            'accounts_with_posts': [],
            'accounts_without_posts': [],
            'total_posts_today': 0,
            'summary': {}
        }
        
        logger.info("Starting Instagram multi-account check...")
        logger.info(f"Checking {len(instagram_accounts)} Instagram accounts")
        
        for username in instagram_accounts:
            has_posts, today_posts = self.check_account_posts_today(username)
            
            account_result = {
                'username': username,
                'platform': 'instagram',
                'has_posts_today': has_posts,
                'posts_count': len(today_posts),
                'post_times': [post.strftime('%H:%M:%S') for post in today_posts]
            }
            
            results['accounts_checked'].append(account_result)
            results['summary'][username] = account_result
            
            if has_posts:
                results['accounts_with_posts'].append(username)
                results['total_posts_today'] += len(today_posts)
            else:
                results['accounts_without_posts'].append(username)
        
        # Log summary
        logger.info("Instagram multi-account check completed:")
        logger.info(f"  Accounts with posts today: {len(results['accounts_with_posts'])}")
        logger.info(f"  Accounts without posts today: {len(results['accounts_without_posts'])}")
        logger.info(f"  Total Instagram posts found today: {results['total_posts_today']}")
        
        return results
