"""Multi-account TikTok monitoring using RapidAPI."""

import logging
import json
import http.client
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any, Tuple

from config import Config

logger = logging.getLogger(__name__)

class MultiAccountTikTokMonitor:
    """TikTok monitor for multiple accounts using RapidAPI."""
    
    def __init__(self):
        self.config = Config()
    
    def _make_api_request(self, sec_uid: str, username: str) -> Optional[Dict[str, Any]]:
        """Make request to TikTok API via RapidAPI for a specific account."""
        try:
            conn = http.client.HTTPSConnection("tiktok-api23.p.rapidapi.com")
            
            headers = {
                'x-rapidapi-key': self.config.RAPIDAPI_KEY,
                'x-rapidapi-host': "tiktok-api23.p.rapidapi.com"
            }
            
            # Request recent posts for the user
            endpoint = f"/api/user/posts?secUid={sec_uid}&count=35&cursor=0"
            
            logger.info(f"Making API request to get posts for @{username}")
            conn.request("GET", endpoint, headers=headers)
            
            res = conn.getresponse()
            data = res.read()
            
            if res.status == 200:
                response_data = json.loads(data.decode("utf-8"))
                logger.info(f"Successfully retrieved data from TikTok API for @{username}")
                return response_data
            else:
                logger.error(f"API request failed for @{username} with status {res.status}: {data.decode('utf-8')}")
                return None
                
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse API response as JSON for @{username}: {e}")
            return None
        except Exception as e:
            logger.error(f"API request failed for @{username}: {e}")
            return None
        finally:
            try:
                conn.close()
            except:
                pass
    
    def _parse_post_dates(self, api_response: Dict[str, Any], username: str) -> List[datetime]:
        """Parse post dates from API response."""
        post_dates = []
        
        try:
            # Get posts from the response
            posts = []
            if 'data' in api_response and 'itemList' in api_response['data']:
                posts = api_response['data']['itemList']
            
            logger.info(f"Found {len(posts)} posts for @{username}")
            
            for i, post in enumerate(posts):
                try:
                    # Get createTime timestamp
                    timestamp = post.get('createTime')
                    
                    if timestamp:
                        # Handle both seconds and milliseconds timestamps
                        if isinstance(timestamp, str):
                            timestamp = int(timestamp)
                        
                        if timestamp > 1000000000000:  # Milliseconds
                            timestamp = timestamp / 1000
                        
                        post_date = datetime.fromtimestamp(timestamp)
                        post_dates.append(post_date)
                        logger.debug(f"@{username} Post {i+1}: {post_date.strftime('%Y-%m-%d %H:%M:%S')}")
                    else:
                        logger.debug(f"@{username} Post {i+1}: No timestamp found")
                    
                except (ValueError, KeyError, TypeError) as e:
                    logger.debug(f"@{username} Post {i+1}: Could not parse timestamp: {e}")
                    continue
            
            logger.info(f"Successfully parsed {len(post_dates)} post dates for @{username}")
            return post_dates
            
        except Exception as e:
            logger.error(f"Error parsing post dates for @{username}: {e}")
            return []
    
    def check_account_posts_today(self, account: Dict[str, str]) -> Tuple[bool, List[datetime]]:
        """Check if there were any posts made today for a specific account."""
        username = account['username']
        sec_uid = account['secUid']
        
        try:
            logger.info(f"Checking posts for @{username}...")
            
            # Make API request
            api_response = self._make_api_request(sec_uid, username)
            if not api_response:
                logger.error(f"Failed to get data from TikTok API for @{username}")
                return False, []
            
            # Parse post dates
            post_dates = self._parse_post_dates(api_response, username)
            
            if not post_dates:
                logger.info(f"No posts found for @{username}")
                return False, []
            
            # Check if any posts were made today
            today = datetime.now().date()
            today_posts = []
            
            for post_date in post_dates:
                if post_date.date() == today:
                    today_posts.append(post_date)
            
            if today_posts:
                logger.info(f"@{username}: Found {len(today_posts)} post(s) from today:")
                for post_date in today_posts:
                    logger.info(f"  - Post at: {post_date.strftime('%Y-%m-%d %H:%M:%S')}")
                return True, today_posts
            else:
                logger.info(f"@{username}: No posts found from today")
                # Show the most recent posts for debugging
                if post_dates:
                    recent_posts = sorted(post_dates, reverse=True)[:2]
                    logger.info(f"@{username}: Most recent posts:")
                    for post_date in recent_posts:
                        logger.info(f"  - {post_date.strftime('%Y-%m-%d %H:%M:%S')}")
                return False, []
            
        except Exception as e:
            logger.error(f"Error checking posts for @{username}: {e}")
            return False, []
    
    def check_all_accounts_today(self) -> Dict[str, Any]:
        """Check all configured accounts for posts today."""
        results = {
            'accounts_checked': [],
            'accounts_with_posts': [],
            'accounts_without_posts': [],
            'total_posts_today': 0,
            'summary': {}
        }
        
        logger.info("Starting multi-account TikTok check...")
        logger.info(f"Checking {len(self.config.TIKTOK_ACCOUNTS)} accounts")
        
        for account in self.config.TIKTOK_ACCOUNTS:
            username = account['username']
            has_posts, today_posts = self.check_account_posts_today(account)
            
            account_result = {
                'username': username,
                'has_posts_today': has_posts,
                'posts_count': len(today_posts),
                'post_times': [post.strftime('%H:%M:%S') for post in today_posts]
            }
            
            results['accounts_checked'].append(account_result)
            results['summary'][username] = account_result
            
            if has_posts:
                results['accounts_with_posts'].append(username)
                results['total_posts_today'] += len(today_posts)
            else:
                results['accounts_without_posts'].append(username)
        
        # Log summary
        logger.info("Multi-account check completed:")
        logger.info(f"  Accounts with posts today: {len(results['accounts_with_posts'])}")
        logger.info(f"  Accounts without posts today: {len(results['accounts_without_posts'])}")
        logger.info(f"  Total posts found today: {results['total_posts_today']}")
        
        return results
    
    def get_status_message(self, results: Dict[str, Any]) -> str:
        """Generate status message for all accounts."""
        date_str = datetime.now().strftime("%Y-%m-%d")
        
        if results['accounts_with_posts']:
            emoji = self.config.SUCCESS_EMOJI
            accounts_with_posts = results['accounts_with_posts']
            total_posts = results['total_posts_today']
            
            if len(accounts_with_posts) == 1:
                status = f"@{accounts_with_posts[0]} posted today ({total_posts} post{'s' if total_posts != 1 else ''})"
            else:
                accounts_str = ', '.join([f"@{acc}" for acc in accounts_with_posts])
                status = f"{accounts_str} posted today ({total_posts} total posts)"
        else:
            emoji = self.config.FAILURE_EMOJI
            all_accounts = [acc['username'] for acc in results['accounts_checked']]
            accounts_str = ', '.join([f"@{acc}" for acc in all_accounts])
            status = f"{accounts_str} - No posts today"
        
        return f"{emoji} {status} ({date_str})"
