"""TikTok monitoring using RapidAPI for real post detection."""

import logging
import json
import http.client
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any

from config import Config

logger = logging.getLogger(__name__)

class APITikTokMonitor:
    """TikTok monitor using RapidAPI for accurate post detection."""
    
    def __init__(self):
        self.config = Config()
    
    def _make_api_request(self) -> Optional[Dict[str, Any]]:
        """Make request to TikTok API via RapidAPI."""
        try:
            conn = http.client.HTTPSConnection("tiktok-api23.p.rapidapi.com")
            
            headers = {
                'x-rapidapi-key': self.config.RAPIDAPI_KEY,
                'x-rapidapi-host': "tiktok-api23.p.rapidapi.com"
            }
            
            # Request recent posts for the user
            endpoint = f"/api/user/posts?secUid={self.config.TIKTOK_SEC_UID}&count=35&cursor=0"
            
            logger.info(f"Making API request to get posts for user: {self.config.TIKTOK_USERNAME}")
            conn.request("GET", endpoint, headers=headers)
            
            res = conn.getresponse()
            data = res.read()
            
            if res.status == 200:
                response_data = json.loads(data.decode("utf-8"))
                logger.info("Successfully retrieved data from TikTok API")
                # Log the response structure for debugging
                logger.debug(f"API Response keys: {list(response_data.keys()) if isinstance(response_data, dict) else 'Not a dict'}")
                if isinstance(response_data, dict) and len(str(response_data)) < 1000:
                    logger.debug(f"Full API Response: {response_data}")
                return response_data
            else:
                logger.error(f"API request failed with status {res.status}: {data.decode('utf-8')}")
                return None
                
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse API response as JSON: {e}")
            return None
        except Exception as e:
            logger.error(f"API request failed: {e}")
            return None
        finally:
            try:
                conn.close()
            except:
                pass
    
    def _parse_post_dates(self, api_response: Dict[str, Any]) -> List[datetime]:
        """Parse post dates from API response."""
        post_dates = []
        
        try:
            # The API response structure may vary, let's handle common patterns
            posts = []
            
            # Try different possible response structures
            if 'data' in api_response:
                if 'itemList' in api_response['data']:
                    posts = api_response['data']['itemList']
                elif 'videos' in api_response['data']:
                    posts = api_response['data']['videos']
                elif 'items' in api_response['data']:
                    posts = api_response['data']['items']
                elif isinstance(api_response['data'], list):
                    posts = api_response['data']
            elif 'itemList' in api_response:
                posts = api_response['itemList']
            elif 'videos' in api_response:
                posts = api_response['videos']
            elif 'items' in api_response:
                posts = api_response['items']
            elif isinstance(api_response, list):
                posts = api_response
            
            logger.info(f"Found {len(posts)} posts in API response")

            for i, post in enumerate(posts):
                try:
                    # Try different timestamp field names
                    timestamp = None
                    timestamp_fields = ['createTime', 'create_time', 'timestamp', 'publishTime', 'publish_time', 'uploadTime']

                    for field in timestamp_fields:
                        if field in post:
                            timestamp = post[field]
                            break
                    
                    if timestamp:
                        # Handle both seconds and milliseconds timestamps
                        if isinstance(timestamp, str):
                            timestamp = int(timestamp)

                        if timestamp > 1000000000000:  # Milliseconds
                            timestamp = timestamp / 1000

                        post_date = datetime.fromtimestamp(timestamp)
                        post_dates.append(post_date)
                        logger.info(f"Post {i+1}: Found post from {post_date.strftime('%Y-%m-%d %H:%M:%S')}")
                    else:
                        logger.debug(f"Post {i+1}: No timestamp found in fields {list(post.keys())}")

                except (ValueError, KeyError, TypeError) as e:
                    logger.debug(f"Post {i+1}: Could not parse timestamp from post: {e}")
                    continue
            
            logger.info(f"Successfully parsed {len(post_dates)} post dates")
            return post_dates
            
        except Exception as e:
            logger.error(f"Error parsing post dates: {e}")
            return []
    
    def check_posts_today(self) -> bool:
        """Check if there were any posts made today using TikTok API."""
        try:
            logger.info("Checking for posts using TikTok API...")
            
            # Make API request
            api_response = self._make_api_request()
            if not api_response:
                logger.error("Failed to get data from TikTok API")
                return False
            
            # Parse post dates
            post_dates = self._parse_post_dates(api_response)
            
            if not post_dates:
                logger.info("No posts found or could not parse post dates")
                return False
            
            # Check if any posts were made today
            today = datetime.now().date()
            today_posts = []
            
            for post_date in post_dates:
                if post_date.date() == today:
                    today_posts.append(post_date)
            
            if today_posts:
                logger.info(f"Found {len(today_posts)} post(s) from today:")
                for post_date in today_posts:
                    logger.info(f"  - Post at: {post_date.strftime('%Y-%m-%d %H:%M:%S')}")
                return True
            else:
                logger.info("No posts found from today")
                # Show the most recent posts for debugging
                if post_dates:
                    recent_posts = sorted(post_dates, reverse=True)[:3]
                    logger.info("Most recent posts:")
                    for post_date in recent_posts:
                        logger.info(f"  - {post_date.strftime('%Y-%m-%d %H:%M:%S')}")
                return False
            
        except Exception as e:
            logger.error(f"Error checking posts: {e}")
            return False
    
    def get_status_message(self, has_posts_today: bool) -> str:
        """Generate status message with emoji."""
        username = self.config.TIKTOK_USERNAME
        date_str = datetime.now().strftime("%Y-%m-%d")
        
        if has_posts_today:
            emoji = self.config.SUCCESS_EMOJI
            status = "Posted today"
        else:
            emoji = self.config.FAILURE_EMOJI
            status = "No posts today"
        
        return f"{emoji} @{username} - {status} ({date_str})"
