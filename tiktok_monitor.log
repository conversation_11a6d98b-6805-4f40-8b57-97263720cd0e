2025-06-08 21:38:38,851 - root - INFO - Tik<PERSON><PERSON> Post Notifier - Single Check Mode
2025-06-08 21:38:38,851 - root - INFO - Timestamp: 2025-06-08 21:38:38.851550
2025-06-08 21:38:38,851 - __main__ - INFO - Starting TikTok post check...
2025-06-08 21:38:38,851 - __main__ - INFO - Checking @naomifoxes for posts today
2025-06-08 21:38:38,851 - WDM - INFO - ====== WebDriver manager ======
2025-06-08 21:38:39,085 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-08 21:38:39,209 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-08 21:38:39,320 - WDM - INFO - Driver [/root/.wdm/drivers/chromedriver/linux64/137.0.7151.68/chromedriver-linux64/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-08 21:38:39,322 - tiktok_monitor - ERROR - Error checking posts: [<PERSON>rrno 8] Exec format error: '/root/.wdm/drivers/chromedriver/linux64/137.0.7151.68/chromedriver-linux64/THIRD_PARTY_NOTICES.chromedriver'
2025-06-08 21:38:39,322 - notifier - INFO - Console notification sent: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:38:39,573 - notifier - ERROR - Slack API error: invalid_auth
2025-06-08 21:38:39,573 - notifier - INFO - Notification sent successfully via: console
2025-06-08 21:38:39,573 - __main__ - INFO - Check completed. Status: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:38:39,573 - __main__ - INFO - Notification results: {'console': True, 'slack': False}
2025-06-08 21:38:39,573 - root - INFO - Check completed successfully
2025-06-08 21:39:48,185 - root - INFO - TikTok Post Notifier - Single Check Mode
2025-06-08 21:39:48,186 - root - INFO - Timestamp: 2025-06-08 21:39:48.186010
2025-06-08 21:39:48,186 - __main__ - INFO - Starting TikTok post check...
2025-06-08 21:39:48,186 - __main__ - INFO - Checking @naomifoxes for posts today
2025-06-08 21:39:48,186 - WDM - INFO - ====== WebDriver manager ======
2025-06-08 21:39:48,366 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-08 21:39:48,479 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-08 21:39:48,584 - WDM - INFO - Driver [/root/.wdm/drivers/chromedriver/linux64/137.0.7151.68/chromedriver-linux64/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-08 21:39:48,585 - tiktok_monitor - INFO - Using Chrome driver at: /root/.wdm/drivers/chromedriver/linux64/137.0.7151.68/chromedriver-linux64/THIRD_PARTY_NOTICES.chromedriver
2025-06-08 21:39:48,586 - tiktok_monitor - ERROR - Failed to setup Chrome driver: [Errno 8] Exec format error: '/root/.wdm/drivers/chromedriver/linux64/137.0.7151.68/chromedriver-linux64/THIRD_PARTY_NOTICES.chromedriver'
2025-06-08 21:39:48,586 - tiktok_monitor - ERROR - Error checking posts: [Errno 8] Exec format error: '/root/.wdm/drivers/chromedriver/linux64/137.0.7151.68/chromedriver-linux64/THIRD_PARTY_NOTICES.chromedriver'
2025-06-08 21:39:48,586 - notifier - INFO - Console notification sent: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:39:48,873 - notifier - ERROR - Slack API error: invalid_auth
2025-06-08 21:39:48,874 - notifier - INFO - Notification sent successfully via: console
2025-06-08 21:39:48,874 - __main__ - INFO - Check completed. Status: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:39:48,874 - __main__ - INFO - Notification results: {'console': True, 'slack': False}
2025-06-08 21:39:48,874 - root - INFO - Check completed successfully
2025-06-08 21:40:27,405 - root - INFO - TikTok Post Notifier - Single Check Mode
2025-06-08 21:40:27,405 - root - INFO - Timestamp: 2025-06-08 21:40:27.405235
2025-06-08 21:40:27,405 - __main__ - INFO - Starting TikTok post check...
2025-06-08 21:40:27,405 - __main__ - INFO - Checking @naomifoxes for posts today
2025-06-08 21:40:27,405 - WDM - INFO - ====== WebDriver manager ======
2025-06-08 21:40:27,565 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-08 21:40:27,670 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-08 21:40:27,781 - WDM - INFO - There is no [linux64] chromedriver "137.0.7151.68" for browser google-chrome "137.0.7151" in cache
2025-06-08 21:40:27,781 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-08 21:40:28,066 - WDM - INFO - WebDriver version 137.0.7151.68 selected
2025-06-08 21:40:28,071 - WDM - INFO - Modern chrome version https://storage.googleapis.com/chrome-for-testing-public/137.0.7151.68/linux64/chromedriver-linux64.zip
2025-06-08 21:40:28,072 - WDM - INFO - About to download new driver from https://storage.googleapis.com/chrome-for-testing-public/137.0.7151.68/linux64/chromedriver-linux64.zip
2025-06-08 21:40:28,151 - WDM - INFO - Driver downloading response is 200
2025-06-08 21:40:28,307 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-08 21:40:28,546 - WDM - INFO - Driver has been saved in cache [/root/.wdm/drivers/chromedriver/linux64/137.0.7151.68]
2025-06-08 21:40:28,548 - tiktok_monitor - INFO - Using Chrome driver at: /root/.wdm/drivers/chromedriver/linux64/137.0.7151.68/chromedriver-linux64/THIRD_PARTY_NOTICES.chromedriver
2025-06-08 21:40:28,549 - tiktok_monitor - ERROR - Failed to setup Chrome driver: [Errno 8] Exec format error: '/root/.wdm/drivers/chromedriver/linux64/137.0.7151.68/chromedriver-linux64/THIRD_PARTY_NOTICES.chromedriver'
2025-06-08 21:40:28,549 - tiktok_monitor - ERROR - Error checking posts: [Errno 8] Exec format error: '/root/.wdm/drivers/chromedriver/linux64/137.0.7151.68/chromedriver-linux64/THIRD_PARTY_NOTICES.chromedriver'
2025-06-08 21:40:28,549 - notifier - INFO - Console notification sent: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:40:28,769 - notifier - ERROR - Slack API error: invalid_auth
2025-06-08 21:40:28,770 - notifier - INFO - Notification sent successfully via: console
2025-06-08 21:40:28,770 - __main__ - INFO - Check completed. Status: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:40:28,770 - __main__ - INFO - Notification results: {'console': True, 'slack': False}
2025-06-08 21:40:28,770 - root - INFO - Check completed successfully
2025-06-08 21:40:56,793 - root - INFO - TikTok Post Notifier - Single Check Mode
2025-06-08 21:40:56,793 - root - INFO - Timestamp: 2025-06-08 21:40:56.793764
2025-06-08 21:40:56,793 - __main__ - INFO - Starting TikTok post check...
2025-06-08 21:40:56,793 - __main__ - INFO - Checking @naomifoxes for posts today
2025-06-08 21:40:56,794 - WDM - INFO - ====== WebDriver manager ======
2025-06-08 21:40:56,957 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-08 21:40:57,067 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-08 21:40:57,113 - WDM - INFO - Driver [/root/.wdm/drivers/chromedriver/linux64/137.0.7151.68/chromedriver-linux64/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-08 21:40:57,114 - tiktok_monitor - INFO - Using Chrome driver at: /root/.wdm/drivers/chromedriver/linux64/137.0.7151.68/chromedriver-linux64/THIRD_PARTY_NOTICES.chromedriver
2025-06-08 21:40:57,115 - tiktok_monitor - ERROR - Failed to setup Chrome driver: [Errno 8] Exec format error: '/root/.wdm/drivers/chromedriver/linux64/137.0.7151.68/chromedriver-linux64/THIRD_PARTY_NOTICES.chromedriver'
2025-06-08 21:40:57,115 - tiktok_monitor - ERROR - Error checking posts: [Errno 8] Exec format error: '/root/.wdm/drivers/chromedriver/linux64/137.0.7151.68/chromedriver-linux64/THIRD_PARTY_NOTICES.chromedriver'
2025-06-08 21:40:57,115 - notifier - INFO - Console notification sent: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:40:57,361 - notifier - ERROR - Slack API error: invalid_auth
2025-06-08 21:40:57,361 - notifier - INFO - Notification sent successfully via: console
2025-06-08 21:40:57,361 - __main__ - INFO - Check completed. Status: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:40:57,361 - __main__ - INFO - Notification results: {'console': True, 'slack': False}
2025-06-08 21:40:57,361 - root - INFO - Check completed successfully
2025-06-08 21:41:31,084 - root - INFO - TikTok Post Notifier - Single Check Mode
2025-06-08 21:41:31,084 - root - INFO - Timestamp: 2025-06-08 21:41:31.084700
2025-06-08 21:41:31,084 - __main__ - INFO - Starting TikTok post check...
2025-06-08 21:41:31,084 - __main__ - INFO - Checking @naomifoxes for posts today
2025-06-08 21:41:31,084 - tiktok_monitor - INFO - Checking for posts using simplified method...
2025-06-08 21:41:33,140 - tiktok_monitor - INFO - Successfully accessed TikTok profile page
2025-06-08 21:41:33,140 - tiktok_monitor - INFO - Simulated: Found posts from today
2025-06-08 21:41:33,140 - notifier - INFO - Console notification sent: ✅ @naomifoxes - Posted today (2025-06-08)
2025-06-08 21:41:33,357 - notifier - ERROR - Slack API error: invalid_auth
2025-06-08 21:41:33,357 - notifier - INFO - Notification sent successfully via: console
2025-06-08 21:41:33,358 - __main__ - INFO - Check completed. Status: ✅ @naomifoxes - Posted today (2025-06-08)
2025-06-08 21:41:33,358 - __main__ - INFO - Notification results: {'console': True, 'slack': False}
2025-06-08 21:41:33,358 - root - INFO - Check completed successfully
2025-06-08 21:41:40,305 - root - INFO - TikTok Post Notifier - Single Check Mode
2025-06-08 21:41:40,305 - root - INFO - Timestamp: 2025-06-08 21:41:40.305486
2025-06-08 21:41:40,305 - __main__ - INFO - Starting TikTok post check...
2025-06-08 21:41:40,305 - __main__ - INFO - Checking @naomifoxes for posts today
2025-06-08 21:41:40,305 - tiktok_monitor - INFO - Checking for posts using simplified method...
2025-06-08 21:41:40,707 - tiktok_monitor - INFO - Successfully accessed TikTok profile page
2025-06-08 21:41:40,707 - tiktok_monitor - INFO - Simulated: Found posts from today
2025-06-08 21:41:40,707 - notifier - INFO - Console notification sent: ✅ @naomifoxes - Posted today (2025-06-08)
2025-06-08 21:41:40,991 - notifier - ERROR - Slack API error: invalid_auth
2025-06-08 21:41:40,991 - notifier - INFO - Notification sent successfully via: console
2025-06-08 21:41:40,991 - __main__ - INFO - Check completed. Status: ✅ @naomifoxes - Posted today (2025-06-08)
2025-06-08 21:41:40,991 - __main__ - INFO - Notification results: {'console': True, 'slack': False}
2025-06-08 21:41:40,991 - root - INFO - Check completed successfully
2025-06-08 21:41:47,025 - root - INFO - TikTok Post Notifier - Scheduled Mode
2025-06-08 21:41:47,026 - root - INFO - Started at: 2025-06-08 21:41:47.026071
2025-06-08 21:41:47,026 - __main__ - INFO - Starting TikTok Post Notifier Scheduler
2025-06-08 21:41:47,026 - __main__ - INFO - Check interval: 1 hour(s)
2025-06-08 21:41:47,026 - __main__ - INFO - Monitoring: @naomifoxes
2025-06-08 21:41:47,026 - __main__ - INFO - Running initial check...
2025-06-08 21:41:47,026 - __main__ - INFO - ==================================================
2025-06-08 21:41:47,026 - __main__ - INFO - Running scheduled TikTok check...
2025-06-08 21:41:47,026 - main - INFO - Starting TikTok post check...
2025-06-08 21:41:47,026 - main - INFO - Checking @naomifoxes for posts today
2025-06-08 21:41:47,026 - tiktok_monitor - INFO - Checking for posts using simplified method...
2025-06-08 21:41:47,364 - tiktok_monitor - INFO - Successfully accessed TikTok profile page
2025-06-08 21:41:47,365 - tiktok_monitor - INFO - Simulated: No posts found from today
2025-06-08 21:41:47,365 - notifier - INFO - Console notification sent: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:41:47,590 - notifier - ERROR - Slack API error: invalid_auth
2025-06-08 21:41:47,590 - notifier - INFO - Notification sent successfully via: console
2025-06-08 21:41:47,590 - main - INFO - Check completed. Status: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:41:47,590 - main - INFO - Notification results: {'console': True, 'slack': False}
2025-06-08 21:41:47,590 - __main__ - INFO - Scheduled check completed successfully
2025-06-08 21:41:47,590 - __main__ - INFO - ==================================================
2025-06-08 21:41:47,591 - __main__ - INFO - Scheduler started. Press Ctrl+C to stop.
2025-06-08 21:41:56,788 - __main__ - INFO - Received signal 15, shutting down gracefully...
2025-06-08 21:42:47,591 - __main__ - INFO - Scheduler stopped.
2025-06-08 21:42:47,591 - root - INFO - Application terminated
2025-06-08 21:44:43,368 - main - INFO - Starting TikTok post check...
2025-06-08 21:44:43,368 - main - INFO - Checking @naomifoxes for posts today
2025-06-08 21:44:43,368 - tiktok_monitor - INFO - Checking for posts using simplified method...
2025-06-08 21:44:43,791 - tiktok_monitor - INFO - Successfully accessed TikTok profile page
2025-06-08 21:44:43,791 - tiktok_monitor - INFO - Simulated: Found posts from today
2025-06-08 21:44:43,791 - notifier - INFO - Console notification sent: ✅ @naomifoxes - Posted today (2025-06-08)
2025-06-08 21:44:44,036 - notifier - ERROR - Slack API error: invalid_auth
2025-06-08 21:44:44,037 - notifier - INFO - Notification sent successfully via: console
2025-06-08 21:44:44,037 - main - INFO - Check completed. Status: ✅ @naomifoxes - Posted today (2025-06-08)
2025-06-08 21:44:44,037 - main - INFO - Notification results: {'console': True, 'slack': False}
2025-06-08 21:44:47,037 - main - INFO - Starting TikTok post check...
2025-06-08 21:44:47,037 - main - INFO - Checking @naomifoxes for posts today
2025-06-08 21:44:47,037 - tiktok_monitor - INFO - Checking for posts using simplified method...
2025-06-08 21:44:47,464 - tiktok_monitor - INFO - Successfully accessed TikTok profile page
2025-06-08 21:44:47,464 - tiktok_monitor - INFO - Simulated: Found posts from today
2025-06-08 21:44:47,465 - notifier - INFO - Console notification sent: ✅ @naomifoxes - Posted today (2025-06-08)
2025-06-08 21:44:47,717 - notifier - ERROR - Slack API error: invalid_auth
2025-06-08 21:44:47,717 - notifier - INFO - Notification sent successfully via: console
2025-06-08 21:44:47,717 - main - INFO - Check completed. Status: ✅ @naomifoxes - Posted today (2025-06-08)
2025-06-08 21:44:47,717 - main - INFO - Notification results: {'console': True, 'slack': False}
2025-06-08 21:44:50,717 - main - INFO - Starting TikTok post check...
2025-06-08 21:44:50,718 - main - INFO - Checking @naomifoxes for posts today
2025-06-08 21:44:50,718 - tiktok_monitor - INFO - Checking for posts using simplified method...
2025-06-08 21:44:51,033 - tiktok_monitor - INFO - Successfully accessed TikTok profile page
2025-06-08 21:44:51,033 - tiktok_monitor - INFO - Simulated: No posts found from today
2025-06-08 21:44:51,033 - notifier - INFO - Console notification sent: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:44:51,228 - notifier - ERROR - Slack API error: invalid_auth
2025-06-08 21:44:51,228 - notifier - INFO - Notification sent successfully via: console
2025-06-08 21:44:51,228 - main - INFO - Check completed. Status: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:44:51,229 - main - INFO - Notification results: {'console': True, 'slack': False}
2025-06-08 21:45:49,625 - root - INFO - TikTok Post Notifier - Single Check Mode
2025-06-08 21:45:49,626 - root - INFO - Timestamp: 2025-06-08 21:45:49.626117
2025-06-08 21:45:49,626 - __main__ - INFO - Starting TikTok post check...
2025-06-08 21:45:49,626 - __main__ - INFO - Checking @naomifoxes for posts today
2025-06-08 21:45:49,626 - tiktok_monitor - INFO - Checking for posts using simplified method...
2025-06-08 21:45:50,064 - tiktok_monitor - INFO - Successfully accessed TikTok profile page
2025-06-08 21:45:50,064 - tiktok_monitor - INFO - Simulated: Found posts from today
2025-06-08 21:45:50,065 - notifier - INFO - Console notification sent: ✅ @naomifoxes - Posted today (2025-06-08)
2025-06-08 21:45:50,300 - notifier - ERROR - Slack API error: invalid_auth
2025-06-08 21:45:50,301 - notifier - INFO - Notification sent successfully via: console
2025-06-08 21:45:50,301 - __main__ - INFO - Check completed. Status: ✅ @naomifoxes - Posted today (2025-06-08)
2025-06-08 21:45:50,301 - __main__ - INFO - Notification results: {'console': True, 'slack': False}
2025-06-08 21:45:50,301 - root - INFO - Check completed successfully
2025-06-08 21:46:34,831 - root - INFO - TikTok Post Notifier - Single Check Mode
2025-06-08 21:46:34,832 - root - INFO - Timestamp: 2025-06-08 21:46:34.832134
2025-06-08 21:46:34,832 - __main__ - INFO - Starting TikTok post check...
2025-06-08 21:46:34,832 - __main__ - INFO - Checking @naomifoxes for posts today
2025-06-08 21:46:34,832 - tiktok_monitor - INFO - Checking for posts using real detection method...
2025-06-08 21:46:35,427 - tiktok_monitor - INFO - Successfully accessed TikTok profile page
2025-06-08 21:46:35,429 - tiktok_monitor - INFO - Found 0 video indicators on page
2025-06-08 21:46:35,430 - tiktok_monitor - INFO - Found 1 recent time indicators
2025-06-08 21:46:35,430 - tiktok_monitor - INFO - No clear indicators of recent posts found
2025-06-08 21:46:35,431 - notifier - INFO - Console notification sent: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:46:35,656 - notifier - ERROR - Slack API error: invalid_auth
2025-06-08 21:46:35,656 - notifier - INFO - Notification sent successfully via: console
2025-06-08 21:46:35,656 - __main__ - INFO - Check completed. Status: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:46:35,656 - __main__ - INFO - Notification results: {'console': True, 'slack': False}
2025-06-08 21:46:35,656 - root - INFO - Check completed successfully
