2025-06-08 21:38:38,851 - root - INFO - Tik<PERSON><PERSON> Post Notifier - Single Check Mode
2025-06-08 21:38:38,851 - root - INFO - Timestamp: 2025-06-08 21:38:38.851550
2025-06-08 21:38:38,851 - __main__ - INFO - Starting TikTok post check...
2025-06-08 21:38:38,851 - __main__ - INFO - Checking @naomifoxes for posts today
2025-06-08 21:38:38,851 - WDM - INFO - ====== WebDriver manager ======
2025-06-08 21:38:39,085 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-08 21:38:39,209 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-08 21:38:39,320 - WDM - INFO - Driver [/root/.wdm/drivers/chromedriver/linux64/137.0.7151.68/chromedriver-linux64/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-08 21:38:39,322 - tiktok_monitor - ERROR - Error checking posts: [<PERSON>rrno 8] Exec format error: '/root/.wdm/drivers/chromedriver/linux64/137.0.7151.68/chromedriver-linux64/THIRD_PARTY_NOTICES.chromedriver'
2025-06-08 21:38:39,322 - notifier - INFO - Console notification sent: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:38:39,573 - notifier - ERROR - Slack API error: invalid_auth
2025-06-08 21:38:39,573 - notifier - INFO - Notification sent successfully via: console
2025-06-08 21:38:39,573 - __main__ - INFO - Check completed. Status: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:38:39,573 - __main__ - INFO - Notification results: {'console': True, 'slack': False}
2025-06-08 21:38:39,573 - root - INFO - Check completed successfully
2025-06-08 21:39:48,185 - root - INFO - TikTok Post Notifier - Single Check Mode
2025-06-08 21:39:48,186 - root - INFO - Timestamp: 2025-06-08 21:39:48.186010
2025-06-08 21:39:48,186 - __main__ - INFO - Starting TikTok post check...
2025-06-08 21:39:48,186 - __main__ - INFO - Checking @naomifoxes for posts today
2025-06-08 21:39:48,186 - WDM - INFO - ====== WebDriver manager ======
2025-06-08 21:39:48,366 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-08 21:39:48,479 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-08 21:39:48,584 - WDM - INFO - Driver [/root/.wdm/drivers/chromedriver/linux64/137.0.7151.68/chromedriver-linux64/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-08 21:39:48,585 - tiktok_monitor - INFO - Using Chrome driver at: /root/.wdm/drivers/chromedriver/linux64/137.0.7151.68/chromedriver-linux64/THIRD_PARTY_NOTICES.chromedriver
2025-06-08 21:39:48,586 - tiktok_monitor - ERROR - Failed to setup Chrome driver: [Errno 8] Exec format error: '/root/.wdm/drivers/chromedriver/linux64/137.0.7151.68/chromedriver-linux64/THIRD_PARTY_NOTICES.chromedriver'
2025-06-08 21:39:48,586 - tiktok_monitor - ERROR - Error checking posts: [Errno 8] Exec format error: '/root/.wdm/drivers/chromedriver/linux64/137.0.7151.68/chromedriver-linux64/THIRD_PARTY_NOTICES.chromedriver'
2025-06-08 21:39:48,586 - notifier - INFO - Console notification sent: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:39:48,873 - notifier - ERROR - Slack API error: invalid_auth
2025-06-08 21:39:48,874 - notifier - INFO - Notification sent successfully via: console
2025-06-08 21:39:48,874 - __main__ - INFO - Check completed. Status: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:39:48,874 - __main__ - INFO - Notification results: {'console': True, 'slack': False}
2025-06-08 21:39:48,874 - root - INFO - Check completed successfully
2025-06-08 21:40:27,405 - root - INFO - TikTok Post Notifier - Single Check Mode
2025-06-08 21:40:27,405 - root - INFO - Timestamp: 2025-06-08 21:40:27.405235
2025-06-08 21:40:27,405 - __main__ - INFO - Starting TikTok post check...
2025-06-08 21:40:27,405 - __main__ - INFO - Checking @naomifoxes for posts today
2025-06-08 21:40:27,405 - WDM - INFO - ====== WebDriver manager ======
2025-06-08 21:40:27,565 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-08 21:40:27,670 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-08 21:40:27,781 - WDM - INFO - There is no [linux64] chromedriver "137.0.7151.68" for browser google-chrome "137.0.7151" in cache
2025-06-08 21:40:27,781 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-08 21:40:28,066 - WDM - INFO - WebDriver version 137.0.7151.68 selected
2025-06-08 21:40:28,071 - WDM - INFO - Modern chrome version https://storage.googleapis.com/chrome-for-testing-public/137.0.7151.68/linux64/chromedriver-linux64.zip
2025-06-08 21:40:28,072 - WDM - INFO - About to download new driver from https://storage.googleapis.com/chrome-for-testing-public/137.0.7151.68/linux64/chromedriver-linux64.zip
2025-06-08 21:40:28,151 - WDM - INFO - Driver downloading response is 200
2025-06-08 21:40:28,307 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-08 21:40:28,546 - WDM - INFO - Driver has been saved in cache [/root/.wdm/drivers/chromedriver/linux64/137.0.7151.68]
2025-06-08 21:40:28,548 - tiktok_monitor - INFO - Using Chrome driver at: /root/.wdm/drivers/chromedriver/linux64/137.0.7151.68/chromedriver-linux64/THIRD_PARTY_NOTICES.chromedriver
2025-06-08 21:40:28,549 - tiktok_monitor - ERROR - Failed to setup Chrome driver: [Errno 8] Exec format error: '/root/.wdm/drivers/chromedriver/linux64/137.0.7151.68/chromedriver-linux64/THIRD_PARTY_NOTICES.chromedriver'
2025-06-08 21:40:28,549 - tiktok_monitor - ERROR - Error checking posts: [Errno 8] Exec format error: '/root/.wdm/drivers/chromedriver/linux64/137.0.7151.68/chromedriver-linux64/THIRD_PARTY_NOTICES.chromedriver'
2025-06-08 21:40:28,549 - notifier - INFO - Console notification sent: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:40:28,769 - notifier - ERROR - Slack API error: invalid_auth
2025-06-08 21:40:28,770 - notifier - INFO - Notification sent successfully via: console
2025-06-08 21:40:28,770 - __main__ - INFO - Check completed. Status: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:40:28,770 - __main__ - INFO - Notification results: {'console': True, 'slack': False}
2025-06-08 21:40:28,770 - root - INFO - Check completed successfully
2025-06-08 21:40:56,793 - root - INFO - TikTok Post Notifier - Single Check Mode
2025-06-08 21:40:56,793 - root - INFO - Timestamp: 2025-06-08 21:40:56.793764
2025-06-08 21:40:56,793 - __main__ - INFO - Starting TikTok post check...
2025-06-08 21:40:56,793 - __main__ - INFO - Checking @naomifoxes for posts today
2025-06-08 21:40:56,794 - WDM - INFO - ====== WebDriver manager ======
2025-06-08 21:40:56,957 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-08 21:40:57,067 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-06-08 21:40:57,113 - WDM - INFO - Driver [/root/.wdm/drivers/chromedriver/linux64/137.0.7151.68/chromedriver-linux64/THIRD_PARTY_NOTICES.chromedriver] found in cache
2025-06-08 21:40:57,114 - tiktok_monitor - INFO - Using Chrome driver at: /root/.wdm/drivers/chromedriver/linux64/137.0.7151.68/chromedriver-linux64/THIRD_PARTY_NOTICES.chromedriver
2025-06-08 21:40:57,115 - tiktok_monitor - ERROR - Failed to setup Chrome driver: [Errno 8] Exec format error: '/root/.wdm/drivers/chromedriver/linux64/137.0.7151.68/chromedriver-linux64/THIRD_PARTY_NOTICES.chromedriver'
2025-06-08 21:40:57,115 - tiktok_monitor - ERROR - Error checking posts: [Errno 8] Exec format error: '/root/.wdm/drivers/chromedriver/linux64/137.0.7151.68/chromedriver-linux64/THIRD_PARTY_NOTICES.chromedriver'
2025-06-08 21:40:57,115 - notifier - INFO - Console notification sent: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:40:57,361 - notifier - ERROR - Slack API error: invalid_auth
2025-06-08 21:40:57,361 - notifier - INFO - Notification sent successfully via: console
2025-06-08 21:40:57,361 - __main__ - INFO - Check completed. Status: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:40:57,361 - __main__ - INFO - Notification results: {'console': True, 'slack': False}
2025-06-08 21:40:57,361 - root - INFO - Check completed successfully
2025-06-08 21:41:31,084 - root - INFO - TikTok Post Notifier - Single Check Mode
2025-06-08 21:41:31,084 - root - INFO - Timestamp: 2025-06-08 21:41:31.084700
2025-06-08 21:41:31,084 - __main__ - INFO - Starting TikTok post check...
2025-06-08 21:41:31,084 - __main__ - INFO - Checking @naomifoxes for posts today
2025-06-08 21:41:31,084 - tiktok_monitor - INFO - Checking for posts using simplified method...
2025-06-08 21:41:33,140 - tiktok_monitor - INFO - Successfully accessed TikTok profile page
2025-06-08 21:41:33,140 - tiktok_monitor - INFO - Simulated: Found posts from today
2025-06-08 21:41:33,140 - notifier - INFO - Console notification sent: ✅ @naomifoxes - Posted today (2025-06-08)
2025-06-08 21:41:33,357 - notifier - ERROR - Slack API error: invalid_auth
2025-06-08 21:41:33,357 - notifier - INFO - Notification sent successfully via: console
2025-06-08 21:41:33,358 - __main__ - INFO - Check completed. Status: ✅ @naomifoxes - Posted today (2025-06-08)
2025-06-08 21:41:33,358 - __main__ - INFO - Notification results: {'console': True, 'slack': False}
2025-06-08 21:41:33,358 - root - INFO - Check completed successfully
2025-06-08 21:41:40,305 - root - INFO - TikTok Post Notifier - Single Check Mode
2025-06-08 21:41:40,305 - root - INFO - Timestamp: 2025-06-08 21:41:40.305486
2025-06-08 21:41:40,305 - __main__ - INFO - Starting TikTok post check...
2025-06-08 21:41:40,305 - __main__ - INFO - Checking @naomifoxes for posts today
2025-06-08 21:41:40,305 - tiktok_monitor - INFO - Checking for posts using simplified method...
2025-06-08 21:41:40,707 - tiktok_monitor - INFO - Successfully accessed TikTok profile page
2025-06-08 21:41:40,707 - tiktok_monitor - INFO - Simulated: Found posts from today
2025-06-08 21:41:40,707 - notifier - INFO - Console notification sent: ✅ @naomifoxes - Posted today (2025-06-08)
2025-06-08 21:41:40,991 - notifier - ERROR - Slack API error: invalid_auth
2025-06-08 21:41:40,991 - notifier - INFO - Notification sent successfully via: console
2025-06-08 21:41:40,991 - __main__ - INFO - Check completed. Status: ✅ @naomifoxes - Posted today (2025-06-08)
2025-06-08 21:41:40,991 - __main__ - INFO - Notification results: {'console': True, 'slack': False}
2025-06-08 21:41:40,991 - root - INFO - Check completed successfully
2025-06-08 21:41:47,025 - root - INFO - TikTok Post Notifier - Scheduled Mode
2025-06-08 21:41:47,026 - root - INFO - Started at: 2025-06-08 21:41:47.026071
2025-06-08 21:41:47,026 - __main__ - INFO - Starting TikTok Post Notifier Scheduler
2025-06-08 21:41:47,026 - __main__ - INFO - Check interval: 1 hour(s)
2025-06-08 21:41:47,026 - __main__ - INFO - Monitoring: @naomifoxes
2025-06-08 21:41:47,026 - __main__ - INFO - Running initial check...
2025-06-08 21:41:47,026 - __main__ - INFO - ==================================================
2025-06-08 21:41:47,026 - __main__ - INFO - Running scheduled TikTok check...
2025-06-08 21:41:47,026 - main - INFO - Starting TikTok post check...
2025-06-08 21:41:47,026 - main - INFO - Checking @naomifoxes for posts today
2025-06-08 21:41:47,026 - tiktok_monitor - INFO - Checking for posts using simplified method...
2025-06-08 21:41:47,364 - tiktok_monitor - INFO - Successfully accessed TikTok profile page
2025-06-08 21:41:47,365 - tiktok_monitor - INFO - Simulated: No posts found from today
2025-06-08 21:41:47,365 - notifier - INFO - Console notification sent: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:41:47,590 - notifier - ERROR - Slack API error: invalid_auth
2025-06-08 21:41:47,590 - notifier - INFO - Notification sent successfully via: console
2025-06-08 21:41:47,590 - main - INFO - Check completed. Status: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:41:47,590 - main - INFO - Notification results: {'console': True, 'slack': False}
2025-06-08 21:41:47,590 - __main__ - INFO - Scheduled check completed successfully
2025-06-08 21:41:47,590 - __main__ - INFO - ==================================================
2025-06-08 21:41:47,591 - __main__ - INFO - Scheduler started. Press Ctrl+C to stop.
2025-06-08 21:41:56,788 - __main__ - INFO - Received signal 15, shutting down gracefully...
2025-06-08 21:42:47,591 - __main__ - INFO - Scheduler stopped.
2025-06-08 21:42:47,591 - root - INFO - Application terminated
2025-06-08 21:44:43,368 - main - INFO - Starting TikTok post check...
2025-06-08 21:44:43,368 - main - INFO - Checking @naomifoxes for posts today
2025-06-08 21:44:43,368 - tiktok_monitor - INFO - Checking for posts using simplified method...
2025-06-08 21:44:43,791 - tiktok_monitor - INFO - Successfully accessed TikTok profile page
2025-06-08 21:44:43,791 - tiktok_monitor - INFO - Simulated: Found posts from today
2025-06-08 21:44:43,791 - notifier - INFO - Console notification sent: ✅ @naomifoxes - Posted today (2025-06-08)
2025-06-08 21:44:44,036 - notifier - ERROR - Slack API error: invalid_auth
2025-06-08 21:44:44,037 - notifier - INFO - Notification sent successfully via: console
2025-06-08 21:44:44,037 - main - INFO - Check completed. Status: ✅ @naomifoxes - Posted today (2025-06-08)
2025-06-08 21:44:44,037 - main - INFO - Notification results: {'console': True, 'slack': False}
2025-06-08 21:44:47,037 - main - INFO - Starting TikTok post check...
2025-06-08 21:44:47,037 - main - INFO - Checking @naomifoxes for posts today
2025-06-08 21:44:47,037 - tiktok_monitor - INFO - Checking for posts using simplified method...
2025-06-08 21:44:47,464 - tiktok_monitor - INFO - Successfully accessed TikTok profile page
2025-06-08 21:44:47,464 - tiktok_monitor - INFO - Simulated: Found posts from today
2025-06-08 21:44:47,465 - notifier - INFO - Console notification sent: ✅ @naomifoxes - Posted today (2025-06-08)
2025-06-08 21:44:47,717 - notifier - ERROR - Slack API error: invalid_auth
2025-06-08 21:44:47,717 - notifier - INFO - Notification sent successfully via: console
2025-06-08 21:44:47,717 - main - INFO - Check completed. Status: ✅ @naomifoxes - Posted today (2025-06-08)
2025-06-08 21:44:47,717 - main - INFO - Notification results: {'console': True, 'slack': False}
2025-06-08 21:44:50,717 - main - INFO - Starting TikTok post check...
2025-06-08 21:44:50,718 - main - INFO - Checking @naomifoxes for posts today
2025-06-08 21:44:50,718 - tiktok_monitor - INFO - Checking for posts using simplified method...
2025-06-08 21:44:51,033 - tiktok_monitor - INFO - Successfully accessed TikTok profile page
2025-06-08 21:44:51,033 - tiktok_monitor - INFO - Simulated: No posts found from today
2025-06-08 21:44:51,033 - notifier - INFO - Console notification sent: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:44:51,228 - notifier - ERROR - Slack API error: invalid_auth
2025-06-08 21:44:51,228 - notifier - INFO - Notification sent successfully via: console
2025-06-08 21:44:51,228 - main - INFO - Check completed. Status: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:44:51,229 - main - INFO - Notification results: {'console': True, 'slack': False}
2025-06-08 21:45:49,625 - root - INFO - TikTok Post Notifier - Single Check Mode
2025-06-08 21:45:49,626 - root - INFO - Timestamp: 2025-06-08 21:45:49.626117
2025-06-08 21:45:49,626 - __main__ - INFO - Starting TikTok post check...
2025-06-08 21:45:49,626 - __main__ - INFO - Checking @naomifoxes for posts today
2025-06-08 21:45:49,626 - tiktok_monitor - INFO - Checking for posts using simplified method...
2025-06-08 21:45:50,064 - tiktok_monitor - INFO - Successfully accessed TikTok profile page
2025-06-08 21:45:50,064 - tiktok_monitor - INFO - Simulated: Found posts from today
2025-06-08 21:45:50,065 - notifier - INFO - Console notification sent: ✅ @naomifoxes - Posted today (2025-06-08)
2025-06-08 21:45:50,300 - notifier - ERROR - Slack API error: invalid_auth
2025-06-08 21:45:50,301 - notifier - INFO - Notification sent successfully via: console
2025-06-08 21:45:50,301 - __main__ - INFO - Check completed. Status: ✅ @naomifoxes - Posted today (2025-06-08)
2025-06-08 21:45:50,301 - __main__ - INFO - Notification results: {'console': True, 'slack': False}
2025-06-08 21:45:50,301 - root - INFO - Check completed successfully
2025-06-08 21:46:34,831 - root - INFO - TikTok Post Notifier - Single Check Mode
2025-06-08 21:46:34,832 - root - INFO - Timestamp: 2025-06-08 21:46:34.832134
2025-06-08 21:46:34,832 - __main__ - INFO - Starting TikTok post check...
2025-06-08 21:46:34,832 - __main__ - INFO - Checking @naomifoxes for posts today
2025-06-08 21:46:34,832 - tiktok_monitor - INFO - Checking for posts using real detection method...
2025-06-08 21:46:35,427 - tiktok_monitor - INFO - Successfully accessed TikTok profile page
2025-06-08 21:46:35,429 - tiktok_monitor - INFO - Found 0 video indicators on page
2025-06-08 21:46:35,430 - tiktok_monitor - INFO - Found 1 recent time indicators
2025-06-08 21:46:35,430 - tiktok_monitor - INFO - No clear indicators of recent posts found
2025-06-08 21:46:35,431 - notifier - INFO - Console notification sent: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:46:35,656 - notifier - ERROR - Slack API error: invalid_auth
2025-06-08 21:46:35,656 - notifier - INFO - Notification sent successfully via: console
2025-06-08 21:46:35,656 - __main__ - INFO - Check completed. Status: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:46:35,656 - __main__ - INFO - Notification results: {'console': True, 'slack': False}
2025-06-08 21:46:35,656 - root - INFO - Check completed successfully
2025-06-08 21:49:26,095 - root - INFO - TikTok Post Notifier - Single Check Mode
2025-06-08 21:49:26,095 - root - INFO - Timestamp: 2025-06-08 21:49:26.095730
2025-06-08 21:49:26,095 - __main__ - INFO - Starting TikTok post check...
2025-06-08 21:49:26,095 - __main__ - INFO - Checking @naomifoxes for posts today
2025-06-08 21:49:26,096 - tiktok_monitor - INFO - Checking for posts using real detection method...
2025-06-08 21:49:26,601 - tiktok_monitor - INFO - Successfully accessed TikTok profile page
2025-06-08 21:49:26,603 - tiktok_monitor - INFO - Found 0 video indicators on page
2025-06-08 21:49:26,603 - tiktok_monitor - INFO - Found 1 recent time indicators
2025-06-08 21:49:26,604 - tiktok_monitor - INFO - No clear indicators of recent posts found
2025-06-08 21:49:26,604 - notifier - INFO - Console notification sent: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:49:26,902 - notifier - ERROR - Slack API error: invalid_auth
2025-06-08 21:49:26,902 - notifier - INFO - Notification sent successfully via: console
2025-06-08 21:49:26,902 - __main__ - INFO - Check completed. Status: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:49:26,903 - __main__ - INFO - Notification results: {'console': True, 'slack': False}
2025-06-08 21:49:26,903 - root - INFO - Check completed successfully
2025-06-08 21:54:54,826 - root - INFO - TikTok Post Notifier - Single Check Mode
2025-06-08 21:54:54,827 - root - INFO - Timestamp: 2025-06-08 21:54:54.827028
2025-06-08 21:54:54,827 - __main__ - INFO - Starting TikTok post check...
2025-06-08 21:54:54,827 - __main__ - INFO - Checking @naomifoxes for posts today
2025-06-08 21:54:54,829 - api_tiktok_monitor - INFO - Checking for posts using TikTok API...
2025-06-08 21:54:54,852 - api_tiktok_monitor - INFO - Making API request to get posts for user: naomifoxes
2025-06-08 21:54:57,135 - api_tiktok_monitor - INFO - Successfully retrieved data from TikTok API
2025-06-08 21:54:57,136 - api_tiktok_monitor - INFO - Found 0 posts in API response
2025-06-08 21:54:57,136 - api_tiktok_monitor - INFO - Successfully parsed 0 post dates
2025-06-08 21:54:57,136 - api_tiktok_monitor - INFO - No posts found or could not parse post dates
2025-06-08 21:54:57,137 - notifier - INFO - Console notification sent: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:54:57,372 - notifier - ERROR - Slack API error: invalid_auth
2025-06-08 21:54:57,372 - notifier - INFO - Notification sent successfully via: console
2025-06-08 21:54:57,372 - __main__ - INFO - Check completed. Status: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:54:57,372 - __main__ - INFO - Notification results: {'console': True, 'slack': False}
2025-06-08 21:54:57,373 - root - INFO - Check completed successfully
2025-06-08 21:55:41,582 - root - INFO - TikTok Post Notifier - Single Check Mode
2025-06-08 21:55:41,582 - root - INFO - Timestamp: 2025-06-08 21:55:41.582824
2025-06-08 21:55:41,582 - __main__ - INFO - Starting TikTok post check...
2025-06-08 21:55:41,582 - __main__ - INFO - Checking @naomifoxes for posts today
2025-06-08 21:55:41,585 - api_tiktok_monitor - INFO - Checking for posts using TikTok API...
2025-06-08 21:55:41,609 - api_tiktok_monitor - INFO - Making API request to get posts for user: naomifoxes
2025-06-08 21:55:43,494 - api_tiktok_monitor - INFO - Successfully retrieved data from TikTok API
2025-06-08 21:55:43,505 - api_tiktok_monitor - INFO - Found 0 posts in API response
2025-06-08 21:55:43,506 - api_tiktok_monitor - INFO - Successfully parsed 0 post dates
2025-06-08 21:55:43,506 - api_tiktok_monitor - INFO - No posts found or could not parse post dates
2025-06-08 21:55:43,506 - notifier - INFO - Console notification sent: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:55:43,731 - notifier - ERROR - Slack API error: invalid_auth
2025-06-08 21:55:43,731 - notifier - INFO - Notification sent successfully via: console
2025-06-08 21:55:43,731 - __main__ - INFO - Check completed. Status: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:55:43,732 - __main__ - INFO - Notification results: {'console': True, 'slack': False}
2025-06-08 21:55:43,732 - root - INFO - Check completed successfully
2025-06-08 21:59:18,059 - root - INFO - TikTok Post Notifier - Single Check Mode
2025-06-08 21:59:18,059 - root - INFO - Timestamp: 2025-06-08 21:59:18.059963
2025-06-08 21:59:18,060 - __main__ - INFO - Starting TikTok post check...
2025-06-08 21:59:18,060 - __main__ - INFO - Checking @naomifoxes for posts today
2025-06-08 21:59:18,062 - api_tiktok_monitor - INFO - Checking for posts using TikTok API...
2025-06-08 21:59:18,087 - api_tiktok_monitor - INFO - Making API request to get posts for user: naomifoxes
2025-06-08 21:59:19,532 - api_tiktok_monitor - INFO - Successfully retrieved data from TikTok API
2025-06-08 21:59:19,543 - api_tiktok_monitor - INFO - Found 35 posts in API response
2025-06-08 21:59:19,543 - api_tiktok_monitor - INFO - Post 1: Found post from 2024-10-18 14:08:09
2025-06-08 21:59:19,543 - api_tiktok_monitor - INFO - Post 2: Found post from 2024-10-15 12:22:05
2025-06-08 21:59:19,543 - api_tiktok_monitor - INFO - Post 3: Found post from 2024-04-19 19:00:37
2025-06-08 21:59:19,544 - api_tiktok_monitor - INFO - Post 4: Found post from 2024-04-19 00:01:22
2025-06-08 21:59:19,544 - api_tiktok_monitor - INFO - Post 5: Found post from 2024-04-17 00:03:55
2025-06-08 21:59:19,544 - api_tiktok_monitor - INFO - Post 6: Found post from 2024-03-04 17:31:23
2025-06-08 21:59:19,544 - api_tiktok_monitor - INFO - Post 7: Found post from 2024-03-01 13:32:01
2025-06-08 21:59:19,544 - api_tiktok_monitor - INFO - Post 8: Found post from 2024-02-13 02:51:18
2025-06-08 21:59:19,544 - api_tiktok_monitor - INFO - Post 9: Found post from 2023-12-13 13:24:15
2025-06-08 21:59:19,544 - api_tiktok_monitor - INFO - Post 10: Found post from 2023-11-27 13:25:54
2025-06-08 21:59:19,544 - api_tiktok_monitor - INFO - Post 11: Found post from 2023-11-13 22:41:43
2025-06-08 21:59:19,544 - api_tiktok_monitor - INFO - Post 12: Found post from 2023-10-12 07:08:34
2025-06-08 21:59:19,544 - api_tiktok_monitor - INFO - Post 13: Found post from 2023-08-31 12:18:43
2025-06-08 21:59:19,545 - api_tiktok_monitor - INFO - Post 14: Found post from 2023-08-10 06:20:17
2025-06-08 21:59:19,545 - api_tiktok_monitor - INFO - Post 15: Found post from 2023-07-29 01:39:18
2025-06-08 21:59:19,545 - api_tiktok_monitor - INFO - Post 16: Found post from 2023-07-21 18:46:36
2025-06-08 21:59:19,545 - api_tiktok_monitor - INFO - Post 17: Found post from 2023-06-24 15:51:46
2025-06-08 21:59:19,545 - api_tiktok_monitor - INFO - Post 18: Found post from 2023-05-06 04:05:01
2025-06-08 21:59:19,545 - api_tiktok_monitor - INFO - Post 19: Found post from 2023-03-31 23:16:55
2025-06-08 21:59:19,545 - api_tiktok_monitor - INFO - Post 20: Found post from 2023-03-25 20:51:15
2025-06-08 21:59:19,545 - api_tiktok_monitor - INFO - Post 21: Found post from 2023-01-26 13:07:39
2025-06-08 21:59:19,545 - api_tiktok_monitor - INFO - Post 22: Found post from 2022-11-09 21:04:08
2025-06-08 21:59:19,545 - api_tiktok_monitor - INFO - Post 23: Found post from 2022-11-04 13:35:36
2025-06-08 21:59:19,545 - api_tiktok_monitor - INFO - Post 24: Found post from 2022-10-25 15:00:23
2025-06-08 21:59:19,545 - api_tiktok_monitor - INFO - Post 25: Found post from 2022-10-21 05:38:04
2025-06-08 21:59:19,546 - api_tiktok_monitor - INFO - Post 26: Found post from 2022-10-17 04:00:40
2025-06-08 21:59:19,546 - api_tiktok_monitor - INFO - Post 27: Found post from 2022-10-07 08:01:43
2025-06-08 21:59:19,546 - api_tiktok_monitor - INFO - Post 28: Found post from 2022-10-07 07:00:38
2025-06-08 21:59:19,546 - api_tiktok_monitor - INFO - Post 29: Found post from 2022-10-07 06:00:07
2025-06-08 21:59:19,546 - api_tiktok_monitor - INFO - Post 30: Found post from 2022-10-07 05:00:28
2025-06-08 21:59:19,546 - api_tiktok_monitor - INFO - Post 31: Found post from 2022-10-07 04:00:34
2025-06-08 21:59:19,546 - api_tiktok_monitor - INFO - Post 32: Found post from 2022-10-06 04:00:33
2025-06-08 21:59:19,546 - api_tiktok_monitor - INFO - Post 33: Found post from 2022-10-05 04:00:15
2025-06-08 21:59:19,546 - api_tiktok_monitor - INFO - Post 34: Found post from 2022-10-03 03:59:28
2025-06-08 21:59:19,546 - api_tiktok_monitor - INFO - Post 35: Found post from 2022-09-30 04:00:13
2025-06-08 21:59:19,546 - api_tiktok_monitor - INFO - Successfully parsed 35 post dates
2025-06-08 21:59:19,546 - api_tiktok_monitor - INFO - No posts found from today
2025-06-08 21:59:19,547 - api_tiktok_monitor - INFO - Most recent posts:
2025-06-08 21:59:19,547 - api_tiktok_monitor - INFO -   - 2024-10-18 14:08:09
2025-06-08 21:59:19,547 - api_tiktok_monitor - INFO -   - 2024-10-15 12:22:05
2025-06-08 21:59:19,547 - api_tiktok_monitor - INFO -   - 2024-04-19 19:00:37
2025-06-08 21:59:19,547 - notifier - INFO - Console notification sent: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:59:19,791 - notifier - ERROR - Slack API error: invalid_auth
2025-06-08 21:59:19,792 - notifier - INFO - Notification sent successfully via: console
2025-06-08 21:59:19,792 - __main__ - INFO - Check completed. Status: ❌ @naomifoxes - No posts today (2025-06-08)
2025-06-08 21:59:19,792 - __main__ - INFO - Notification results: {'console': True, 'slack': False}
2025-06-08 21:59:19,792 - root - INFO - Check completed successfully
2025-06-08 22:03:03,162 - root - INFO - TikTok Post Notifier - Single Check Mode
2025-06-08 22:03:03,163 - root - INFO - Timestamp: 2025-06-08 22:03:03.162978
2025-06-08 22:03:03,163 - __main__ - INFO - Starting TikTok post check...
2025-06-08 22:03:03,163 - __main__ - INFO - Checking @naomifoxes for posts today
2025-06-08 22:03:03,163 - api_tiktok_monitor - INFO - Checking for posts using TikTok API...
2025-06-08 22:03:03,201 - api_tiktok_monitor - INFO - Making API request to get posts for user: naomifoxes
2025-06-08 22:03:04,887 - api_tiktok_monitor - INFO - Successfully retrieved data from TikTok API
2025-06-08 22:03:04,903 - api_tiktok_monitor - INFO - Found 36 posts in API response
2025-06-08 22:03:04,904 - api_tiktok_monitor - INFO - Post 1: Found post from 2025-02-18 22:21:22
2025-06-08 22:03:04,904 - api_tiktok_monitor - INFO - Post 2: Found post from 2025-06-08 21:41:41
2025-06-08 22:03:04,904 - api_tiktok_monitor - INFO - Post 3: Found post from 2025-06-08 21:40:55
2025-06-08 22:03:04,904 - api_tiktok_monitor - INFO - Post 4: Found post from 2025-06-08 21:40:02
2025-06-08 22:03:04,904 - api_tiktok_monitor - INFO - Post 5: Found post from 2025-06-08 21:39:07
2025-06-08 22:03:04,904 - api_tiktok_monitor - INFO - Post 6: Found post from 2025-06-07 23:54:14
2025-06-08 22:03:04,904 - api_tiktok_monitor - INFO - Post 7: Found post from 2025-06-07 23:53:10
2025-06-08 22:03:04,904 - api_tiktok_monitor - INFO - Post 8: Found post from 2025-06-07 23:52:12
2025-06-08 22:03:04,904 - api_tiktok_monitor - INFO - Post 9: Found post from 2025-06-07 23:50:54
2025-06-08 22:03:04,905 - api_tiktok_monitor - INFO - Post 10: Found post from 2025-06-06 22:27:46
2025-06-08 22:03:04,905 - api_tiktok_monitor - INFO - Post 11: Found post from 2025-06-06 22:26:56
2025-06-08 22:03:04,905 - api_tiktok_monitor - INFO - Post 12: Found post from 2025-06-06 22:25:10
2025-06-08 22:03:04,905 - api_tiktok_monitor - INFO - Post 13: Found post from 2025-06-06 22:14:41
2025-06-08 22:03:04,905 - api_tiktok_monitor - INFO - Post 14: Found post from 2025-06-05 22:33:38
2025-06-08 22:03:04,905 - api_tiktok_monitor - INFO - Post 15: Found post from 2025-06-05 22:32:50
2025-06-08 22:03:04,905 - api_tiktok_monitor - INFO - Post 16: Found post from 2025-06-05 22:32:03
2025-06-08 22:03:04,905 - api_tiktok_monitor - INFO - Post 17: Found post from 2025-06-05 22:30:30
2025-06-08 22:03:04,905 - api_tiktok_monitor - INFO - Post 18: Found post from 2025-06-04 23:45:25
2025-06-08 22:03:04,905 - api_tiktok_monitor - INFO - Post 19: Found post from 2025-06-04 23:44:46
2025-06-08 22:03:04,905 - api_tiktok_monitor - INFO - Post 20: Found post from 2025-06-04 23:44:08
2025-06-08 22:03:04,905 - api_tiktok_monitor - INFO - Post 21: Found post from 2025-06-04 23:43:23
2025-06-08 22:03:04,906 - api_tiktok_monitor - INFO - Post 22: Found post from 2025-06-04 03:18:20
2025-06-08 22:03:04,906 - api_tiktok_monitor - INFO - Post 23: Found post from 2025-06-04 03:17:15
2025-06-08 22:03:04,906 - api_tiktok_monitor - INFO - Post 24: Found post from 2025-06-04 03:13:40
2025-06-08 22:03:04,906 - api_tiktok_monitor - INFO - Post 25: Found post from 2025-06-04 03:11:58
2025-06-08 22:03:04,906 - api_tiktok_monitor - INFO - Post 26: Found post from 2025-05-31 16:28:13
2025-06-08 22:03:04,906 - api_tiktok_monitor - INFO - Post 27: Found post from 2025-05-31 16:27:31
2025-06-08 22:03:04,906 - api_tiktok_monitor - INFO - Post 28: Found post from 2025-05-31 16:26:59
2025-06-08 22:03:04,906 - api_tiktok_monitor - INFO - Post 29: Found post from 2025-05-31 16:25:36
2025-06-08 22:03:04,906 - api_tiktok_monitor - INFO - Post 30: Found post from 2025-05-31 13:02:38
2025-06-08 22:03:04,906 - api_tiktok_monitor - INFO - Post 31: Found post from 2025-05-31 13:01:56
2025-06-08 22:03:04,906 - api_tiktok_monitor - INFO - Post 32: Found post from 2025-05-31 13:01:20
2025-06-08 22:03:04,906 - api_tiktok_monitor - INFO - Post 33: Found post from 2025-05-31 13:00:48
2025-06-08 22:03:04,907 - api_tiktok_monitor - INFO - Post 34: Found post from 2025-05-30 15:42:49
2025-06-08 22:03:04,907 - api_tiktok_monitor - INFO - Post 35: Found post from 2025-05-30 15:41:52
2025-06-08 22:03:04,907 - api_tiktok_monitor - INFO - Post 36: Found post from 2025-05-30 15:41:07
2025-06-08 22:03:04,907 - api_tiktok_monitor - INFO - Successfully parsed 36 post dates
2025-06-08 22:03:04,907 - api_tiktok_monitor - INFO - Found 4 post(s) from today:
2025-06-08 22:03:04,907 - api_tiktok_monitor - INFO -   - Post at: 2025-06-08 21:41:41
2025-06-08 22:03:04,908 - api_tiktok_monitor - INFO -   - Post at: 2025-06-08 21:40:55
2025-06-08 22:03:04,908 - api_tiktok_monitor - INFO -   - Post at: 2025-06-08 21:40:02
2025-06-08 22:03:04,908 - api_tiktok_monitor - INFO -   - Post at: 2025-06-08 21:39:07
2025-06-08 22:03:04,908 - notifier - INFO - Console notification sent: ✅ @naomifoxes - Posted today (2025-06-08)
2025-06-08 22:03:05,149 - notifier - ERROR - Slack API error: invalid_auth
2025-06-08 22:03:05,149 - notifier - INFO - Notification sent successfully via: console
2025-06-08 22:03:05,149 - __main__ - INFO - Check completed. Status: ✅ @naomifoxes - Posted today (2025-06-08)
2025-06-08 22:03:05,150 - __main__ - INFO - Notification results: {'console': True, 'slack': False}
2025-06-08 22:03:05,150 - root - INFO - Check completed successfully
