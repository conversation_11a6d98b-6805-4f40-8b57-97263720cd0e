# TikTok Post Notifier

A Python application that monitors TikTok profiles for new posts and sends notifications with status updates.

## Features

- 🔍 Monitors TikTok profiles for daily posts
- ✅ Reports with checkmark emoji if posts were made today
- ❌ Reports with X emoji if no posts were made today
- 📅 Scheduled monitoring with configurable intervals
- 🔔 Multiple notification channels (<PERSON>sol<PERSON>, Slack)
- 🤖 Multiple scraping strategies (requests + Selenium fallback)
- 📝 Comprehensive logging and error handling
- 🧪 Full test suite included
- 🐳 Ready for containerization

## Quick Start

1. **Setup the environment:**
   ```bash
   chmod +x setup.sh
   ./setup.sh
   ```

2. **Configure the application:**
   ```bash
   cp .env.example .env
   # Edit .env with your settings
   ```

3. **Run a single check:**
   ```bash
   source venv/bin/activate
   python main.py
   ```

4. **Run with scheduling:**
   ```bash
   source venv/bin/activate
   python run.py
   ```

## Configuration

Edit the `.env` file to configure the application:

### Required Settings
- `TIKTOK_USERNAME`: TikTok username to monitor (default: naomifoxes)

### Optional Settings
- `CHECK_INTERVAL_HOURS`: How often to check (default: 1)
- `SLACK_BOT_TOKEN`: Slack bot token for notifications
- `SLACK_CHANNEL`: Slack channel to post to (default: #general)
- `HEADLESS_BROWSER`: Run browser in headless mode (default: true)
- `LOG_LEVEL`: Logging level (default: INFO)

## Slack Integration

To enable Slack notifications:

1. Create a Slack app and bot token
2. Add the bot to your workspace
3. Set `SLACK_BOT_TOKEN` in your `.env` file
4. Set `SLACK_CHANNEL` to your desired channel

## Usage Examples

### Single Check
```bash
python main.py
```

### Scheduled Monitoring
```bash
python run.py
```

### Custom Configuration
```bash
export TIKTOK_USERNAME=yourusername
export CHECK_INTERVAL_HOURS=2
python run.py
```

## Output Examples

**When posts are found:**
```
✅ @naomifoxes - Posted today (2024-01-15)
```

**When no posts are found:**
```
❌ @naomifoxes - No posts today (2024-01-15)
```

## Dependencies

- selenium==4.15.2
- webdriver-manager==4.0.1
- python-dotenv==1.0.0
- schedule==1.2.0
- requests==2.32.3
- slack-sdk==3.27.1

## Testing

Run the included test suite:

```bash
source venv/bin/activate
python test_app.py
```

## Project Structure

```
post-notifier/
├── main.py                    # Single check entry point
├── run.py                     # Scheduled monitoring entry point
├── tiktok_monitor.py          # Basic TikTok monitoring
├── enhanced_tiktok_monitor.py # Enhanced monitoring with multiple strategies
├── notifier.py                # Notification handling
├── config.py                  # Configuration management
├── test_app.py                # Test suite
├── requirements.txt           # Python dependencies
├── setup.sh                   # Setup script
├── .env.example               # Environment variables template
└── README.md                  # This file
```

## Troubleshooting

### Chrome Driver Issues
- The app automatically downloads and manages Chrome drivers using webdriver-manager
- If you encounter driver issues, delete `/root/.wdm` and restart the app

### TikTok Access Issues
- Ensure you have a stable internet connection
- TikTok may block automated access; the app uses multiple strategies to handle this
- Some profiles may be private or restricted
- Consider adjusting the `CHECK_INTERVAL_HOURS` to avoid rate limiting

### Logging
- Check the log file (`tiktok_monitor.log`) for detailed error information
- Adjust `LOG_LEVEL` in `.env` for more or less verbose logging

### Slack Integration Issues
- Verify your `SLACK_BOT_TOKEN` is correct
- Ensure the bot has permission to post in the specified channel
- Check that the channel name includes the `#` prefix

## Advanced Usage

### Using Enhanced Monitor
For better TikTok scraping, you can modify `main.py` to use the enhanced monitor:

```python
from enhanced_tiktok_monitor import EnhancedTikTokMonitor

# Replace TikTokMonitor() with:
monitor = EnhancedTikTokMonitor()
```

### Custom Scheduling
Modify the scheduling in `run.py` for different intervals:

```python
# Check every 30 minutes
schedule.every(30).minutes.do(self.run_scheduled_check)

# Check at specific time daily
schedule.every().day.at("09:00").do(self.run_scheduled_check)
```

### Docker Deployment
Create a `Dockerfile`:

```dockerfile
FROM python:3.12-slim

RUN apt-get update && apt-get install -y \
    wget gnupg google-chrome-stable \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
CMD ["python", "run.py"]
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

MIT License
