"""Configuration management for TikTok Post Notifier."""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Configuration class for the TikTok monitor."""
    
    # TikTok Settings
    TIKTOK_USERNAME = os.getenv('TIKTOK_USERNAME', 'naomifoxes')
    TIKTOK_URL = f"https://www.tiktok.com/@{TIKTOK_USERNAME}"
    CHECK_INTERVAL_HOURS = int(os.getenv('CHECK_INTERVAL_HOURS', '1'))

    # TikTok API Settings
    RAPIDAPI_KEY = os.getenv('RAPIDAPI_KEY', '**************************************************')
    TIKTOK_SEC_UID = os.getenv('TIKTOK_SEC_UID', 'MS4wLjABAAAAa87Em-uSLvPBWUtYIItk5wy_RbmotwT1Z-VUBhqrWN9ie-_4cFH2pAQxogrHWaWH')
    
    # Notification Settings
    SLACK_BOT_TOKEN = os.getenv('SLACK_BOT_TOKEN')
    SLACK_CHANNEL = os.getenv('SLACK_CHANNEL', '#general')
    CONSOLE_OUTPUT = os.getenv('CONSOLE_OUTPUT', 'true').lower() == 'true'
    
    # Logging
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'DEBUG')
    LOG_FILE = os.getenv('LOG_FILE', 'tiktok_monitor.log')
    
    # Browser Settings
    HEADLESS_BROWSER = os.getenv('HEADLESS_BROWSER', 'true').lower() == 'true'
    BROWSER_TIMEOUT = int(os.getenv('BROWSER_TIMEOUT', '30'))
    
    # Status Emojis
    SUCCESS_EMOJI = "✅"
    FAILURE_EMOJI = "❌"
    
    @classmethod
    def validate(cls):
        """Validate configuration settings."""
        if not cls.TIKTOK_USERNAME:
            raise ValueError("TIKTOK_USERNAME is required")

        if not cls.RAPIDAPI_KEY:
            raise ValueError("RAPIDAPI_KEY is required")

        if not cls.TIKTOK_SEC_UID:
            raise ValueError("TIKTOK_SEC_UID is required")

        if cls.SLACK_BOT_TOKEN and not cls.SLACK_CHANNEL:
            raise ValueError("SLACK_CHANNEL is required when SLACK_BOT_TOKEN is provided")

        return True
