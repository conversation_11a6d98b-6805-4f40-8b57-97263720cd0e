"""Entry point with scheduling for TikTok Post Notifier."""

import logging
import schedule
import time
import signal
import sys
from datetime import datetime

from main import setup_logging, run_check
from config import Config

class TikTokScheduler:
    """Scheduler for TikTok post monitoring."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.running = True
        
        # Set up signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        self.logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.running = False
    
    def run_scheduled_check(self):
        """Wrapper for run_check with additional logging."""
        self.logger.info("=" * 50)
        self.logger.info("Running scheduled TikTok check...")
        
        result = run_check()
        
        if result['success']:
            self.logger.info("Scheduled check completed successfully")
        else:
            self.logger.error(f"Scheduled check failed: {result.get('error', 'Unknown error')}")
        
        self.logger.info("=" * 50)
        return result
    
    def start_scheduler(self):
        """Start the scheduler with configured interval."""
        config = Config()
        
        self.logger.info("Starting TikTok Post Notifier Scheduler")
        self.logger.info(f"Check interval: {config.CHECK_INTERVAL_HOURS} hour(s)")
        self.logger.info(f"Monitoring: @{config.TIKTOK_USERNAME}")
        
        # Schedule the job
        schedule.every(config.CHECK_INTERVAL_HOURS).hours.do(self.run_scheduled_check)
        
        # Run initial check
        self.logger.info("Running initial check...")
        self.run_scheduled_check()
        
        # Main scheduler loop
        self.logger.info("Scheduler started. Press Ctrl+C to stop.")
        
        while self.running:
            try:
                schedule.run_pending()
                time.sleep(60)  # Check every minute for pending jobs
            except KeyboardInterrupt:
                break
            except Exception as e:
                self.logger.error(f"Scheduler error: {e}")
                time.sleep(60)  # Wait before retrying
        
        self.logger.info("Scheduler stopped.")

def main():
    """Main entry point for scheduled monitoring."""
    # Set up logging
    logger = setup_logging()
    
    logger.info("TikTok Post Notifier - Scheduled Mode")
    logger.info(f"Started at: {datetime.now()}")
    
    try:
        # Validate configuration
        Config.validate()
        
        # Start scheduler
        scheduler = TikTokScheduler()
        scheduler.start_scheduler()
        
    except KeyboardInterrupt:
        logger.info("Interrupted by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)
    
    logger.info("Application terminated")

if __name__ == "__main__":
    main()
