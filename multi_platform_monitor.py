"""Multi-platform monitoring for TikTok and Instagram."""

import logging
from datetime import datetime
from typing import Dict, Any, List

from config import Config
from multi_account_tiktok_monitor import MultiAccountTikTokMonitor
from instagram_monitor import InstagramMonitor

logger = logging.getLogger(__name__)

class MultiPlatformMonitor:
    """Monitor multiple platforms (TikTok and Instagram) for posts."""
    
    def __init__(self):
        self.config = Config()
        self.tiktok_monitor = MultiAccountTikTokMonitor()
        self.instagram_monitor = InstagramMonitor()
    
    def check_all_platforms_today(self) -> Dict[str, Any]:
        """Check all platforms and accounts for posts today."""
        results = {
            'platforms': {},
            'all_accounts_checked': [],
            'all_accounts_with_posts': [],
            'all_accounts_without_posts': [],
            'total_posts_today': 0,
            'platform_summary': {}
        }
        
        logger.info("Starting multi-platform check...")
        
        # Check TikTok accounts
        if self.config.TIKTOK_ACCOUNTS:
            logger.info("Checking TikTok accounts...")
            tiktok_results = self.tiktok_monitor.check_all_accounts_today()
            results['platforms']['tiktok'] = tiktok_results
            
            # Add TikTok results to overall summary
            for account in tiktok_results['accounts_checked']:
                account['platform'] = 'tiktok'
                results['all_accounts_checked'].append(account)
                
                if account['has_posts_today']:
                    results['all_accounts_with_posts'].append({
                        'username': account['username'],
                        'platform': 'tiktok',
                        'posts_count': account['posts_count']
                    })
                else:
                    results['all_accounts_without_posts'].append({
                        'username': account['username'],
                        'platform': 'tiktok'
                    })
            
            results['total_posts_today'] += tiktok_results['total_posts_today']
        
        # Check Instagram accounts
        if self.config.INSTAGRAM_ACCOUNTS:
            logger.info("Checking Instagram accounts...")
            instagram_results = self.instagram_monitor.check_all_accounts_today(self.config.INSTAGRAM_ACCOUNTS)
            results['platforms']['instagram'] = instagram_results
            
            # Add Instagram results to overall summary
            for account in instagram_results['accounts_checked']:
                account['platform'] = 'instagram'
                results['all_accounts_checked'].append(account)
                
                if account['has_posts_today']:
                    results['all_accounts_with_posts'].append({
                        'username': account['username'],
                        'platform': 'instagram',
                        'posts_count': account['posts_count']
                    })
                else:
                    results['all_accounts_without_posts'].append({
                        'username': account['username'],
                        'platform': 'instagram'
                    })
            
            results['total_posts_today'] += instagram_results['total_posts_today']
        
        # Create platform summary
        results['platform_summary'] = {
            'tiktok': {
                'accounts_checked': len(self.config.TIKTOK_ACCOUNTS),
                'accounts_with_posts': len(results['platforms'].get('tiktok', {}).get('accounts_with_posts', [])),
                'total_posts': results['platforms'].get('tiktok', {}).get('total_posts_today', 0)
            },
            'instagram': {
                'accounts_checked': len(self.config.INSTAGRAM_ACCOUNTS),
                'accounts_with_posts': len(results['platforms'].get('instagram', {}).get('accounts_with_posts', [])),
                'total_posts': results['platforms'].get('instagram', {}).get('total_posts_today', 0)
            }
        }
        
        # Log overall summary
        logger.info("Multi-platform check completed:")
        logger.info(f"  Total accounts checked: {len(results['all_accounts_checked'])}")
        logger.info(f"  Accounts with posts today: {len(results['all_accounts_with_posts'])}")
        logger.info(f"  Accounts without posts today: {len(results['all_accounts_without_posts'])}")
        logger.info(f"  Total posts found today: {results['total_posts_today']}")
        
        return results
    
    def get_status_message(self, results: Dict[str, Any]) -> str:
        """Generate status message for all platforms and accounts."""
        date_str = datetime.now().strftime("%Y-%m-%d")
        
        if results['all_accounts_with_posts']:
            emoji = self.config.SUCCESS_EMOJI
            
            # Group accounts by platform
            platform_groups = {}
            for account in results['all_accounts_with_posts']:
                platform = account['platform']
                if platform not in platform_groups:
                    platform_groups[platform] = []
                platform_groups[platform].append(account)
            
            # Create detailed status for each platform
            platform_lines = []
            
            # TikTok accounts
            if 'tiktok' in platform_groups:
                tiktok_lines = []
                for account_summary in platform_groups['tiktok']:
                    username = account_summary['username']
                    # Get detailed info from platform results
                    account_detail = None
                    for acc in results['platforms']['tiktok']['accounts_checked']:
                        if acc['username'] == username:
                            account_detail = acc
                            break
                    
                    if account_detail:
                        post_count = account_detail['posts_count']
                        post_times = account_detail['post_times']
                        times_str = ', '.join(post_times[:3]) + ('...' if len(post_times) > 3 else '')
                        tiktok_lines.append(f"🎵 @{username}: {post_count} post{'s' if post_count != 1 else ''} ({times_str})")
                
                if tiktok_lines:
                    platform_lines.extend(tiktok_lines)
            
            # Instagram accounts
            if 'instagram' in platform_groups:
                instagram_lines = []
                for account_summary in platform_groups['instagram']:
                    username = account_summary['username']
                    # Get detailed info from platform results
                    account_detail = None
                    for acc in results['platforms']['instagram']['accounts_checked']:
                        if acc['username'] == username:
                            account_detail = acc
                            break
                    
                    if account_detail:
                        post_count = account_detail['posts_count']
                        post_times = account_detail['post_times']
                        times_str = ', '.join(post_times[:3]) + ('...' if len(post_times) > 3 else '')
                        instagram_lines.append(f"📸 @{username}: {post_count} post{'s' if post_count != 1 else ''} ({times_str})")
                
                if instagram_lines:
                    platform_lines.extend(instagram_lines)
            
            # Combine all platform lines
            if len(platform_lines) == 1:
                status = platform_lines[0]
            else:
                total_posts = results['total_posts_today']
                status = f"Multiple accounts posted today:\n" + '\n'.join(platform_lines)
                status += f"\nTotal: {total_posts} posts across all platforms"
        
        else:
            emoji = self.config.FAILURE_EMOJI
            account_lines = []
            
            # Show all accounts that didn't post
            for account in results['all_accounts_checked']:
                username = account['username']
                platform = account['platform']
                platform_emoji = "🎵" if platform == "tiktok" else "📸"
                account_lines.append(f"{platform_emoji} @{username}: No posts today")
            
            if len(account_lines) == 1:
                status = account_lines[0]
            else:
                status = '\n'.join(account_lines)
        
        return f"{emoji} {status} ({date_str})"
