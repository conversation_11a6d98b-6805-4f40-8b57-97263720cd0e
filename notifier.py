"""Notification functionality for TikTok Post Notifier."""

import logging
from typing import Optional

from slack_sdk import WebClient
from slack_sdk.errors import SlackApiError

from config import Config

logger = logging.getLogger(__name__)

class Notifier:
    """Handle notifications via various channels."""
    
    def __init__(self):
        self.config = Config()
        self.slack_client = None
        
        if self.config.SLACK_BOT_TOKEN:
            self.slack_client = WebClient(token=self.config.SLACK_BOT_TOKEN)
    
    def send_console_notification(self, message: str) -> bool:
        """Send notification to console."""
        if self.config.CONSOLE_OUTPUT:
            print(f"[NOTIFICATION] {message}")
            logger.info(f"Console notification sent: {message}")
            return True
        return False
    
    def send_slack_notification(self, message: str) -> bool:
        """Send notification to Slack."""
        if not self.slack_client:
            logger.debug("Slack client not configured")
            return False
        
        try:
            response = self.slack_client.chat_postMessage(
                channel=self.config.SLACK_CHANNEL,
                text=message
            )
            
            if response["ok"]:
                logger.info(f"Slack notification sent successfully: {message}")
                return True
            else:
                logger.error(f"Slack notification failed: {response}")
                return False
                
        except SlackApiError as e:
            logger.error(f"Slack API error: {e.response['error']}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error sending Slack notification: {e}")
            return False
    
    def send_notification(self, message: str) -> dict:
        """Send notification via all configured channels."""
        results = {
            'console': False,
            'slack': False
        }
        
        # Always try console if enabled
        results['console'] = self.send_console_notification(message)
        
        # Try Slack if configured
        if self.slack_client:
            results['slack'] = self.send_slack_notification(message)
        
        # Log overall result
        successful_channels = [channel for channel, success in results.items() if success]
        if successful_channels:
            logger.info(f"Notification sent successfully via: {', '.join(successful_channels)}")
        else:
            logger.warning("No notifications were sent successfully")
        
        return results
